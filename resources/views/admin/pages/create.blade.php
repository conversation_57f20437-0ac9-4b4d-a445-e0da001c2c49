@extends('layouts.admin')
@push('title', 'Tạo trang mới')
@push('meta')@endpush
@push('css')@endpush

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-document-add me-2"></i>
                    Tạo trang mới
                </h4>

                <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-arrow-left"></span>
                    <span>Quay lại</span>
                </a>
            </div>
        </div>
    </div>

    <form action="{{ route('admin.pages.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="ol-card radius-8px">
                    <div class="ol-card-body p-20px">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label ol-form-label">Tiêu đề trang <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control ol-form-control @error('title') is-invalid @enderror" 
                                   value="{{ old('title') }}" placeholder="Nhập tiêu đề trang" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label ol-form-label">Đường dẫn URL</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ url('/') }}/</span>
                                <input type="text" name="slug" id="slug" class="form-control ol-form-control @error('slug') is-invalid @enderror" 
                                       value="{{ old('slug') }}" placeholder="duong-dan-url">
                            </div>
                            <small class="form-text text-muted">Để trống để tự động tạo từ tiêu đề</small>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <label for="content" class="form-label ol-form-label">Nội dung HTML <span class="text-danger">*</span></label>
                            <textarea name="content" id="content" class="form-control ol-form-control @error('content') is-invalid @enderror"
                                      rows="20" required placeholder="Dán mã HTML của bạn vào đây...">{{ old('content') }}</textarea>
                            <small class="form-text text-muted">Dán trực tiếp mã HTML hoàn chỉnh vào đây. Hỗ trợ tất cả thẻ HTML và CSS inline.</small>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>


            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Publish Settings -->
                <div class="ol-card radius-8px">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-settings me-2"></i>
                            Cài đặt xuất bản
                        </h5>

                        <div class="mb-3">
                            <label for="status" class="form-label ol-form-label">Trạng thái</label>
                            <select name="status" id="status" class="form-select ol-form-control @error('status') is-invalid @enderror" required>
                                <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Bản nháp</option>
                                <option value="published" {{ old('status') == 'published' ? 'selected' : '' }}>Đã xuất bản</option>
                                <option value="private" {{ old('status') == 'private' ? 'selected' : '' }}>Riêng tư</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="show_in_menu" id="show_in_menu" 
                                       value="1" {{ old('show_in_menu') ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_in_menu">
                                    Hiển thị trong menu
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="sort_order" class="form-label ol-form-label">Thứ tự sắp xếp</label>
                            <input type="number" name="sort_order" id="sort_order" class="form-control ol-form-control @error('sort_order') is-invalid @enderror" 
                                   value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="template" class="form-label ol-form-label">Template</label>
                            <select name="template" id="template" class="form-select ol-form-control @error('template') is-invalid @enderror">
                                <option value="default" {{ old('template') == 'default' ? 'selected' : '' }}>Mặc định</option>
                                <option value="full-width" {{ old('template') == 'full-width' ? 'selected' : '' }}>Toàn màn hình</option>
                                <option value="sidebar" {{ old('template') == 'sidebar' ? 'selected' : '' }}>Có sidebar</option>
                            </select>
                            @error('template')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-picture me-2"></i>
                            Ảnh đại diện
                        </h5>

                        <div class="mb-3">
                            <input type="file" name="featured_image" id="featured_image" 
                                   class="form-control ol-form-control @error('featured_image') is-invalid @enderror" 
                                   accept="image/*">
                            <small class="form-text text-muted">Chấp nhận: JPG, PNG, GIF. Tối đa 2MB</small>
                            @error('featured_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div id="image-preview" class="text-center" style="display: none;">
                            <img id="preview-img" src="" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn ol-btn-primary">
                                <i class="fi-rr-disk me-1"></i> Lưu trang
                            </button>
                            <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-secondary">
                                <i class="fi-rr-cross me-1"></i> Hủy bỏ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>


@endsection

@push('js')
<script>
$(document).ready(function() {
    // Enhanced Summernote configuration for better HTML support
    $('#content').summernote('destroy'); // Destroy default initialization
    $('#content').summernote({
        height: 400,
        minHeight: 300,
        maxHeight: 800,
        focus: false,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
            ['fontname', ['fontname']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['height', ['height']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'video', 'hr']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        styleTags: [
            'p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
        ],
        fontNames: [
            'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',
            'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',
            'Tahoma', 'Times New Roman', 'Verdana', 'Roboto', 'Open Sans'
        ],
        fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '36', '48'],
        codemirror: {
            theme: 'monokai',
            lineNumbers: true,
            mode: 'htmlmixed'
        },
        callbacks: {
            onPaste: function (e) {
                // Allow pasting HTML content
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();

                // Insert the pasted content as HTML
                $(this).summernote('pasteHTML', bufferText);
            },
            onInit: function() {
                // Add custom button for HTML import
                var ui = $.summernote.ui;
                var button = ui.button({
                    contents: '<i class="fa fa-code"></i> Import HTML',
                    tooltip: 'Import HTML Code',
                    click: function () {
                        var htmlCode = prompt('Paste your HTML code here:');
                        if (htmlCode) {
                            $('#content').summernote('code', htmlCode);
                        }
                    }
                });
                $('.note-toolbar').append(button.render());
            }
        }
    });

    // Auto generate slug from title
    $('#title').on('keyup', function() {
        let title = $(this).val();
        let slug = title.toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .trim('-'); // Remove leading/trailing hyphens

        // Convert Vietnamese characters
        slug = slug.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a');
        slug = slug.replace(/[èéẹẻẽêềếệểễ]/g, 'e');
        slug = slug.replace(/[ìíịỉĩ]/g, 'i');
        slug = slug.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o');
        slug = slug.replace(/[ùúụủũưừứựửữ]/g, 'u');
        slug = slug.replace(/[ỳýỵỷỹ]/g, 'y');
        slug = slug.replace(/đ/g, 'd');

        $('#slug').val(slug);
    });

    // Image preview
    $('#featured_image').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
        }
    });

});

// Function to import HTML code from modal
function importHtmlCode() {
    var htmlCode = $('#htmlCodeInput').val();
    if (htmlCode.trim()) {
        $('#content').summernote('code', htmlCode);
        $('#htmlImportModal').modal('hide');
        $('#htmlCodeInput').val(''); // Clear the textarea

        // Show success message
        alert('HTML đã được import thành công!');
    } else {
        alert('Vui lòng nhập mã HTML!');
    }
}

// Function to clear content
function clearContent() {
    if (confirm('Bạn có chắc chắn muốn xóa toàn bộ nội dung?')) {
        $('#content').summernote('code', '');
    }
}
</script>
@endpush
