@extends('layouts.admin')
@push('title', 'Chỉnh sửa trang')
@push('meta')@endpush
@push('css')
<link rel="stylesheet" href="{{ asset('assets/global/summernote/summernote-lite.min.css') }}">
@endpush

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-edit me-2"></i>
                    Chỉnh sửa trang: {{ $page->title }}
                </h4>

                <div class="d-flex gap-2">
                    <a href="{{ $page->url }}" target="_blank" class="btn ol-btn-outline-primary d-flex align-items-center cg-10px">
                        <span class="fi-rr-eye"></span>
                        <span>Xem trang</span>
                    </a>
                    <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                        <span class="fi-rr-arrow-left"></span>
                        <span>Quay lại</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form action="{{ route('admin.pages.update', $page->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="ol-card radius-8px">
                    <div class="ol-card-body p-20px">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label ol-form-label">Tiêu đề trang <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control ol-form-control @error('title') is-invalid @enderror" 
                                   value="{{ old('title', $page->title) }}" placeholder="Nhập tiêu đề trang" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label ol-form-label">Đường dẫn URL</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ url('/') }}/</span>
                                <input type="text" name="slug" id="slug" class="form-control ol-form-control @error('slug') is-invalid @enderror" 
                                       value="{{ old('slug', $page->slug) }}" placeholder="duong-dan-url">
                            </div>
                            <small class="form-text text-muted">Để trống để tự động tạo từ tiêu đề</small>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label for="content" class="form-label ol-form-label mb-0">Nội dung <span class="text-danger">*</span></label>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#htmlImportModal">
                                        <i class="fi-rr-code"></i> Import HTML
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearContent()">
                                        <i class="fi-rr-trash"></i> Xóa nội dung
                                    </button>
                                    <a href="{{ route('view', ['path' => 'admin.pages.help']) }}" target="_blank" class="btn btn-sm btn-outline-info">
                                        <i class="fi-rr-info"></i> Hướng dẫn
                                    </a>
                                </div>
                            </div>
                            <textarea name="content" id="content" class="form-control text_editor @error('content') is-invalid @enderror"
                                      rows="15" required>{{ old('content', $page->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Excerpt -->
                        <div class="mb-3">
                            <label for="excerpt" class="form-label ol-form-label">Tóm tắt</label>
                            <textarea name="excerpt" id="excerpt" class="form-control ol-form-control @error('excerpt') is-invalid @enderror" 
                                      rows="3" placeholder="Tóm tắt ngắn về nội dung trang">{{ old('excerpt', $page->excerpt) }}</textarea>
                            @error('excerpt')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-search me-2"></i>
                            Cài đặt SEO
                        </h5>

                        <div class="mb-3">
                            <label for="meta_title" class="form-label ol-form-label">Meta Title</label>
                            <input type="text" name="meta_title" id="meta_title" class="form-control ol-form-control @error('meta_title') is-invalid @enderror" 
                                   value="{{ old('meta_title', $page->meta_title) }}" placeholder="Tiêu đề SEO">
                            @error('meta_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label ol-form-label">Meta Description</label>
                            <textarea name="meta_description" id="meta_description" class="form-control ol-form-control @error('meta_description') is-invalid @enderror" 
                                      rows="3" placeholder="Mô tả SEO">{{ old('meta_description', $page->meta_description) }}</textarea>
                            @error('meta_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label ol-form-label">Meta Keywords</label>
                            <input type="text" name="meta_keywords" id="meta_keywords" class="form-control ol-form-control @error('meta_keywords') is-invalid @enderror" 
                                   value="{{ old('meta_keywords', $page->meta_keywords) }}" placeholder="từ khóa, seo, website">
                            @error('meta_keywords')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Page Info -->
                <div class="ol-card radius-8px">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-info me-2"></i>
                            Thông tin trang
                        </h5>
                        
                        <div class="mb-2">
                            <small class="text-muted">Người tạo:</small><br>
                            <strong>{{ $page->creator->name ?? 'N/A' }}</strong>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">Ngày tạo:</small><br>
                            <strong>{{ $page->created_at->format('d/m/Y H:i') }}</strong>
                        </div>
                        
                        @if($page->updated_at != $page->created_at)
                        <div class="mb-2">
                            <small class="text-muted">Cập nhật lần cuối:</small><br>
                            <strong>{{ $page->updated_at->format('d/m/Y H:i') }}</strong>
                            @if($page->updater)
                                <br><small>bởi {{ $page->updater->name }}</small>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Publish Settings -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-settings me-2"></i>
                            Cài đặt xuất bản
                        </h5>

                        <div class="mb-3">
                            <label for="status" class="form-label ol-form-label">Trạng thái</label>
                            <select name="status" id="status" class="form-select ol-form-control @error('status') is-invalid @enderror" required>
                                <option value="draft" {{ old('status', $page->status) == 'draft' ? 'selected' : '' }}>Bản nháp</option>
                                <option value="published" {{ old('status', $page->status) == 'published' ? 'selected' : '' }}>Đã xuất bản</option>
                                <option value="private" {{ old('status', $page->status) == 'private' ? 'selected' : '' }}>Riêng tư</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="show_in_menu" id="show_in_menu" 
                                       value="1" {{ old('show_in_menu', $page->show_in_menu) ? 'checked' : '' }}>
                                <label class="form-check-label" for="show_in_menu">
                                    Hiển thị trong menu
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="sort_order" class="form-label ol-form-label">Thứ tự sắp xếp</label>
                            <input type="number" name="sort_order" id="sort_order" class="form-control ol-form-control @error('sort_order') is-invalid @enderror" 
                                   value="{{ old('sort_order', $page->sort_order) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="template" class="form-label ol-form-label">Template</label>
                            <select name="template" id="template" class="form-select ol-form-control @error('template') is-invalid @enderror">
                                <option value="default" {{ old('template', $page->template) == 'default' ? 'selected' : '' }}>Mặc định</option>
                                <option value="full-width" {{ old('template', $page->template) == 'full-width' ? 'selected' : '' }}>Toàn màn hình</option>
                                <option value="sidebar" {{ old('template', $page->template) == 'sidebar' ? 'selected' : '' }}>Có sidebar</option>
                            </select>
                            @error('template')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-picture me-2"></i>
                            Ảnh đại diện
                        </h5>

                        @if($page->featured_image)
                        <div class="mb-3 text-center">
                            <img src="{{ $page->featured_image_url }}" class="img-fluid rounded" style="max-height: 200px;">
                            <div class="mt-2">
                                <small class="text-muted">Ảnh hiện tại</small>
                            </div>
                        </div>
                        @endif

                        <div class="mb-3">
                            <input type="file" name="featured_image" id="featured_image" 
                                   class="form-control ol-form-control @error('featured_image') is-invalid @enderror" 
                                   accept="image/*">
                            <small class="form-text text-muted">Chấp nhận: JPG, PNG, GIF. Tối đa 2MB</small>
                            @error('featured_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div id="image-preview" class="text-center" style="display: none;">
                            <img id="preview-img" src="" class="img-fluid rounded" style="max-height: 200px;">
                            <div class="mt-2">
                                <small class="text-muted">Ảnh mới</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn ol-btn-primary">
                                <i class="fi-rr-disk me-1"></i> Cập nhật trang
                            </button>
                            <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-secondary">
                                <i class="fi-rr-cross me-1"></i> Hủy bỏ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- HTML Import Modal -->
    <div class="modal fade" id="htmlImportModal" tabindex="-1" aria-labelledby="htmlImportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="htmlImportModalLabel">
                        <i class="fi-rr-code me-2"></i>Import HTML Code
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="htmlCodeInput" class="form-label">Dán mã HTML của bạn vào đây:</label>
                        <textarea id="htmlCodeInput" class="form-control" rows="15"
                                  placeholder="<div>
    <h1>Tiêu đề của bạn</h1>
    <p>Nội dung của bạn...</p>
</div>"></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fi-rr-info me-2"></i>
                        <strong>Lưu ý:</strong> Bạn có thể dán toàn bộ mã HTML của một trang web hoặc chỉ một phần HTML.
                        Hệ thống sẽ tự động xử lý và hiển thị trong editor.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="importHtmlCode()">
                        <i class="fi-rr-check me-1"></i>Import HTML
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Enhanced Summernote configuration for better HTML support
    $('#content').summernote('destroy'); // Destroy default initialization
    $('#content').summernote({
        height: 400,
        minHeight: 300,
        maxHeight: 800,
        focus: false,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
            ['fontname', ['fontname']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['height', ['height']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'video', 'hr']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        styleTags: [
            'p', 'blockquote', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
        ],
        fontNames: [
            'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',
            'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',
            'Tahoma', 'Times New Roman', 'Verdana', 'Roboto', 'Open Sans'
        ],
        fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '36', '48'],
        codemirror: {
            theme: 'monokai',
            lineNumbers: true,
            mode: 'htmlmixed'
        },
        callbacks: {
            onPaste: function (e) {
                // Allow pasting HTML content
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();

                // Insert the pasted content as HTML
                $(this).summernote('pasteHTML', bufferText);
            }
        }
    });

    // Auto generate slug from title (only if slug is empty)
    $('#title').on('keyup', function() {
        let currentSlug = $('#slug').val();
        if (!currentSlug) {
            let title = $(this).val();
            let slug = title.toLowerCase()
                .replace(/[^\w\s-]/g, '') // Remove special characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
                .trim('-'); // Remove leading/trailing hyphens

            // Convert Vietnamese characters
            slug = slug.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a');
            slug = slug.replace(/[èéẹẻẽêềếệểễ]/g, 'e');
            slug = slug.replace(/[ìíịỉĩ]/g, 'i');
            slug = slug.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o');
            slug = slug.replace(/[ùúụủũưừứựửữ]/g, 'u');
            slug = slug.replace(/[ỳýỵỷỹ]/g, 'y');
            slug = slug.replace(/đ/g, 'd');

            $('#slug').val(slug);
        }
    });

    // Image preview
    $('#featured_image').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
        }
    });

});

// Function to import HTML code from modal
function importHtmlCode() {
    var htmlCode = $('#htmlCodeInput').val();
    if (htmlCode.trim()) {
        $('#content').summernote('code', htmlCode);
        $('#htmlImportModal').modal('hide');
        $('#htmlCodeInput').val(''); // Clear the textarea

        // Show success message
        alert('HTML đã được import thành công!');
    } else {
        alert('Vui lòng nhập mã HTML!');
    }
}

// Function to clear content
function clearContent() {
    if (confirm('Bạn có chắc chắn muốn xóa toàn bộ nội dung?')) {
        $('#content').summernote('code', '');
    }
}
</script>
@endpush
