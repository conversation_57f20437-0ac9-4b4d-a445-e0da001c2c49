@extends('layouts.admin')
@push('title', 'Quản lý trang')
@push('meta')@endpush
@push('css')
<style>
.page-title-link {
    transition: all 0.2s ease;
}
.page-title-link:hover {
    text-decoration: underline !important;
    color: #0056b3 !important;
}
</style>
@endpush

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-document me-2"></i>
                    Quản lý trang
                </h4>

                <a href="{{ route('admin.pages.create') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-plus"></span>
                    <span>Tạo trang mới</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="ol-card radius-8px mt-3">
        <div class="ol-card-body py-3 px-20px">
            <form method="GET" action="{{ route('admin.pages.index') }}" class="row g-3">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control ol-form-control" 
                           placeholder="Tìm kiếm theo tiêu đề, nội dung..." 
                           value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select ol-form-control">
                        <option value="">Tất cả trạng thái</option>
                        <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Đã xuất bản</option>
                        <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Bản nháp</option>
                        <option value="private" {{ request('status') == 'private' ? 'selected' : '' }}>Riêng tư</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn ol-btn-primary">
                        <i class="fi-rr-search me-1"></i> Tìm kiếm
                    </button>
                </div>
                <div class="col-md-3 text-end">
                    <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-secondary">
                        <i class="fi-rr-refresh me-1"></i> Làm mới
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Pages List -->
    <div class="ol-card radius-8px mt-3">
        <div class="ol-card-body p-20px">
            @if($pages->count() > 0)
                <div class="table-responsive">
                    <table class="table ol-table">
                        <thead>
                            <tr>
                                <th>Tiêu đề</th>
                                <th>Đường dẫn</th>
                                <th>Trạng thái</th>
                                <th>Hiển thị menu</th>
                                <th>Người tạo</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($pages as $page)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($page->featured_image)
                                                <img src="{{ $page->featured_image_url }}"
                                                     class="rounded me-2"
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            @endif
                                            <div>
                                                <a href="{{ route('admin.pages.edit', $page->id) }}" class="text-decoration-none page-title-link">
                                                    <strong class="text-primary">{{ $page->title }}</strong>
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code>/{{ $page->slug }}</code>
                                        <a href="{{ $page->url }}" target="_blank" class="ms-1">
                                            <i class="fi-rr-external-link"></i>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge 
                                            @if($page->status == 'published') bg-success
                                            @elseif($page->status == 'draft') bg-warning
                                            @else bg-secondary
                                            @endif">
                                            {{ $page->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($page->show_in_menu)
                                            <span class="badge bg-info">Có</span>
                                        @else
                                            <span class="badge bg-light text-dark">Không</span>
                                        @endif
                                    </td>
                                    <td>{{ $page->creator->name ?? 'N/A' }}</td>
                                    <td>{{ $page->created_at->format('d/m/Y H:i') }}</td>
                                    <td>
                                        <div class="dropdown ol-icon-dropdown ol-icon-dropdown-transparent">
                                            <button class="btn ol-btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <span class="fi-rr-menu-dots-vertical"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.pages.edit', $page->id) }}">
                                                        <span class="fi-rr-edit"></span> Chỉnh sửa
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ $page->url }}" target="_blank">
                                                        <span class="fi-rr-eye"></span> Xem trang
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.pages.status', $page->id) }}">
                                                        <span class="fi-rr-refresh"></span> 
                                                        @if($page->status == 'published') Chuyển về nháp @else Xuất bản @endif
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('admin.pages.duplicate', $page->id) }}">
                                                        <span class="fi-rr-copy"></span> Sao chép
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" 
                                                       href="{{ route('admin.pages.delete', $page->id) }}"
                                                       onclick="return confirm('Bạn có chắc chắn muốn xóa trang này?')">
                                                        <span class="fi-rr-trash"></span> Xóa
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $pages->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fi-rr-document fs-48px text-muted mb-3"></i>
                    <h5 class="text-muted">Chưa có trang nào</h5>
                    <p class="text-muted">Hãy tạo trang đầu tiên của bạn</p>
                    <a href="{{ route('admin.pages.create') }}" class="btn ol-btn-primary">
                        <i class="fi-rr-plus me-1"></i> Tạo trang mới
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
