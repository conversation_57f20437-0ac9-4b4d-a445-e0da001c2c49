@extends('layouts.default')
@push('title', $page->meta_title ?: $page->title)
@push('meta')
    @if($page->meta_description)
        <meta name="description" content="{{ $page->meta_description }}">
    @endif
    @if($page->meta_keywords)
        <meta name="keywords" content="{{ $page->meta_keywords }}">
    @endif
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ $page->meta_title ?: $page->title }}">
    @if($page->meta_description)
        <meta property="og:description" content="{{ $page->meta_description }}">
    @endif
    @if($page->featured_image)
        <meta property="og:image" content="{{ $page->featured_image_url }}">
    @endif
    <meta property="og:url" content="{{ $page->url }}">
    <meta property="og:type" content="article">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ $page->meta_title ?: $page->title }}">
    @if($page->meta_description)
        <meta name="twitter:description" content="{{ $page->meta_description }}">
    @endif
    @if($page->featured_image)
        <meta name="twitter:image" content="{{ $page->featured_image_url }}">
    @endif
@endpush

@push('css')
<style>
.page-content {
    line-height: 1.8;
}

.page-content h1, .page-content h2, .page-content h3, 
.page-content h4, .page-content h5, .page-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.page-content h1 { font-size: 2.5rem; }
.page-content h2 { font-size: 2rem; }
.page-content h3 { font-size: 1.75rem; }
.page-content h4 { font-size: 1.5rem; }
.page-content h5 { font-size: 1.25rem; }
.page-content h6 { font-size: 1.1rem; }

.page-content p {
    margin-bottom: 1.5rem;
}

.page-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
}

.page-content blockquote {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
}

.page-content ul, .page-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.page-content li {
    margin-bottom: 0.5rem;
}

.page-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
}

.page-content table th,
.page-content table td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    text-align: left;
}

.page-content table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.page-content code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.page-content pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    margin-bottom: 3rem;
}

.page-meta {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 2rem;
}

@if($page->template == 'full-width')
.container-fluid {
    padding-left: 0;
    padding-right: 0;
}
@endif
</style>
@endpush

@section('content')
@if($page->template == 'default' || $page->template == 'sidebar')
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-4 mb-3">{{ $page->title }}</h1>
                    @if($page->excerpt)
                        <p class="lead">{{ $page->excerpt }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Page Content -->
    <div class="container">
        <div class="row">
            @if($page->template == 'sidebar')
                <div class="col-lg-8">
            @else
                <div class="col-lg-10 mx-auto">
            @endif
                <!-- Page Meta -->
                <div class="page-meta">
                    <i class="fi-rr-calendar"></i> 
                    Cập nhật: {{ $page->updated_at->format('d/m/Y') }}
                    @if($page->creator)
                        | <i class="fi-rr-user"></i> {{ $page->creator->name }}
                    @endif
                </div>

                <!-- Featured Image -->
                @if($page->featured_image)
                    <div class="text-center mb-4">
                        <img src="{{ $page->featured_image_url }}" 
                             alt="{{ $page->title }}" 
                             class="img-fluid rounded shadow">
                    </div>
                @endif

                <!-- Page Content -->
                <div class="page-content">
                    {!! $page->content !!}
                </div>

                <!-- Share Buttons -->
                <div class="mt-5 pt-4 border-top">
                    <h5>Chia sẻ trang này:</h5>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode($page->url) }}" 
                           target="_blank" class="btn btn-primary btn-sm">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode($page->url) }}&text={{ urlencode($page->title) }}" 
                           target="_blank" class="btn btn-info btn-sm">
                            <i class="fab fa-twitter"></i> Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode($page->url) }}" 
                           target="_blank" class="btn btn-primary btn-sm">
                            <i class="fab fa-linkedin-in"></i> LinkedIn
                        </a>
                        <button class="btn btn-secondary btn-sm" onclick="copyToClipboard('{{ $page->url }}')">
                            <i class="fi-rr-copy"></i> Sao chép link
                        </button>
                    </div>
                </div>
            </div>

            @if($page->template == 'sidebar')
                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="sticky-top" style="top: 2rem;">
                        <!-- Related Pages or Widgets -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Trang khác</h5>
                            </div>
                            <div class="card-body">
                                @php
                                    $relatedPages = App\Models\Page::published()
                                        ->where('id', '!=', $page->id)
                                        ->limit(5)
                                        ->get();
                                @endphp
                                
                                @if($relatedPages->count() > 0)
                                    @foreach($relatedPages as $relatedPage)
                                        <div class="mb-3">
                                            <a href="{{ $relatedPage->url }}" class="text-decoration-none">
                                                <h6 class="mb-1">{{ $relatedPage->title }}</h6>
                                            </a>
                                            @if($relatedPage->excerpt)
                                                <small class="text-muted">{{ Str::limit($relatedPage->excerpt, 80) }}</small>
                                            @endif
                                        </div>
                                        @if(!$loop->last)
                                            <hr>
                                        @endif
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">Chưa có trang nào khác.</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

@elseif($page->template == 'full-width')
    <!-- Full Width Template -->
    <div class="page-content">
        {!! $page->content !!}
    </div>
@endif
@endsection

@push('js')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        alert('Đã sao chép link vào clipboard!');
    }, function(err) {
        console.error('Không thể sao chép: ', err);
    });
}
</script>
@endpush
