@extends('layouts.default')
@push('title', '<PERSON><PERSON><PERSON> ho<PERSON>t kh<PERSON>a học')
@push('meta')@endpush
@push('css')
<style>
    .activation-card {
        background: #ffffff;
        border-radius: 16px;
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 40px 30px;
        text-align: center;
        border: 1px solid #e2e8f0;
    }

    .activation-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .activation-icon svg {
        width: 40px;
        height: 40px;
        fill: white;
    }

    .activation-title {
        font-size: 32px;
        font-weight: 700;
        color: #ff9800;
        margin-bottom: 15px;
        line-height: 1.2;
    }

    .activation-subtitle {
        color: #64748b;
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 40px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .activation-form {
        position: relative;
    }

    .activation-input-group {
        position: relative;
        margin-bottom: 30px;
    }

    .activation-input {
        width: 100%;
        padding: 20px 24px;
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        letter-spacing: 3px;
        background: #f8fafc;
        color: #334155;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-transform: uppercase;
    }

    .activation-input::placeholder {
        color: #94a3b8;
        font-weight: 500;
        letter-spacing: 1px;
        text-transform: none;
    }

    .activation-input:focus {
        outline: none;
        border-color: #667eea;
        background: #ffffff;
        box-shadow:
            0 0 0 4px rgba(102, 126, 234, 0.1),
            0 10px 25px rgba(102, 126, 234, 0.15);
        transform: translateY(-2px);
    }

    .activation-input:valid {
        border-color: #10b981;
        background: #f0fdf4;
    }

    .activation-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 16px;
        padding: 18px 48px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow:
            0 10px 25px rgba(102, 126, 234, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        position: relative;
        overflow: hidden;
        min-width: 200px;
    }

    .activation-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .activation-btn:hover {
        transform: translateY(-3px);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }

    .activation-btn:hover::before {
        left: 100%;
    }

    .activation-btn:active {
        transform: translateY(-1px);
    }

    .activation-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 30px;
        margin-top: 50px;
        padding-top: 40px;
        border-top: 1px solid #e2e8f0;
    }

    .activation-feature {
        text-align: center;
        padding: 20px;
    }

    .activation-feature-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        opacity: 0.8;
    }

    .activation-feature-icon svg {
        width: 24px;
        height: 24px;
        fill: white;
    }

    .activation-feature-title {
        font-size: 16px;
        font-weight: 600;
        color: #334155;
        margin-bottom: 8px;
    }

    .activation-feature-desc {
        font-size: 14px;
        color: #64748b;
        line-height: 1.5;
    }

    @media (max-width: 768px) {
        .activation-card {
            margin: 20px;
            padding: 40px 30px;
        }

        .activation-title {
            font-size: 28px;
        }

        .activation-input {
            font-size: 16px;
            padding: 18px 20px;
        }

        .activation-btn {
            padding: 16px 40px;
            font-size: 16px;
        }

        .activation-features {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }
</style>
@endpush
@section('content')
<section class="course-content">
    <div class="container">
        <div class="row">
            @include('frontend.default.student.left_sidebar')
            <div class="col-lg-9">
                <div class="activation-card">
                    <!-- Icon -->
                    <div class="activation-icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1M12 7C13.4 7 14.8 8.6 14.8 10V11.5C15.4 11.5 16 12.4 16 13V16C16 17.4 15.4 18 14.8 18H9.2C8.6 18 8 17.4 8 16V13C8 12.4 8.6 11.5 9.2 11.5V10C9.2 8.6 10.6 7 12 7M12 8.2C11.2 8.2 10.5 8.7 10.5 10V11.5H13.5V10C13.5 8.7 12.8 8.2 12 8.2Z"/>
                        </svg>
                    </div>

                    <!-- Title & Subtitle -->
                    <h1 class="activation-title">Nhập mã kích hoạt</h1>
                    <p class="activation-subtitle">
                        Nhập mã kích hoạt của bạn vào ô bên dưới để bắt đầu khóa học.
                    </p>

                    <!-- Form -->
                    <form id="activationForm" class="activation-form">
                        @csrf
                        <div class="activation-input-group">
                            <input
                                type="text"
                                name="activation_code"
                                id="activation_code"
                                class="activation-input"
                                maxlength="20"
                                placeholder="Nhập mã kích hoạt"
                                required
                                autofocus
                            >
                        </div>
                        <button type="submit" class="activation-btn" id="activationBtn">
                            <span class="btn-text">Kích hoạt</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> Đang xử lý...
                            </span>
                        </button>
                    </form>

                    <!-- Alert Messages -->
                    <div id="alertMessage" style="display: none; margin-top: 20px;">
                        <div class="alert" id="alertContent"></div>
                    </div>

                    <!-- Features -->
                    <div class="activation-features">
                        <div class="activation-feature">
                            <div class="activation-feature-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
                                </svg>
                            </div>
                            <h3 class="activation-feature-title">Truy cập ngay lập tức</h3>
                            <p class="activation-feature-desc">Kích hoạt và bắt đầu học ngay</p>
                        </div>
                        <div class="activation-feature">
                            <div class="activation-feature-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17C10.89,17 10,16.1 10,15C10,13.89 10.89,13 12,13A2,2 0 0,1 14,15A2,2 0 0,1 12,17M18,8A6,6 0 0,1 12,14A6,6 0 0,1 6,8A6,6 0 0,1 12,2A6,6 0 0,1 18,8Z"/>
                                </svg>
                            </div>
                            <h3 class="activation-feature-title">Bảo mật cao</h3>
                            <p class="activation-feature-desc">Mã kích hoạt được mã hóa an toàn</p>
                        </div>
                        <div class="activation-feature">
                            <div class="activation-feature-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2M21,9V7L15,1H5C3.89,1 3,1.89 3,3V21A2,2 0 0,0 5,23H19A2,2 0 0,0 21,21V9M19,9H14V4H5V19H19V9Z"/>
                                </svg>
                            </div>
                            <h3 class="activation-feature-title">Hỗ trợ 24/7</h3>
                            <p class="activation-feature-desc">Luôn sẵn sàng hỗ trợ bạn</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
