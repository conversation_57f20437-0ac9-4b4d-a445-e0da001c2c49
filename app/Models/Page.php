<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Page extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'status',
        'featured_image',
        'sort_order',
        'show_in_menu',
        'template',
        'custom_fields',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'custom_fields' => 'array',
        'show_in_menu' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Tự động tạo slug khi tạo mới
        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = static::generateUniqueSlug($page->title);
            }
            if (empty($page->created_by) && auth()->id()) {
                $page->created_by = auth()->id();
            }
        });

        // Cập nhật updated_by khi chỉnh sửa
        static::updating(function ($page) {
            if (auth()->id()) {
                $page->updated_by = auth()->id();
            }
        });
    }

    /**
     * Tạo slug duy nhất
     */
    public static function generateUniqueSlug($title, $id = null)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)->when($id, function ($query, $id) {
            return $query->where('id', '!=', $id);
        })->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Scope cho các trang đã xuất bản
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope cho các trang hiển thị trong menu
     */
    public function scopeInMenu($query)
    {
        return $query->where('show_in_menu', true)->orderBy('sort_order');
    }

    /**
     * Relationship với user tạo
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship với user cập nhật
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Accessor cho URL trang
     */
    public function getUrlAttribute()
    {
        return route('page.show', $this->slug);
    }

    /**
     * Accessor cho trạng thái hiển thị
     */
    public function getStatusLabelAttribute()
    {
        $labels = [
            'published' => 'Đã xuất bản',
            'draft' => 'Bản nháp',
            'private' => 'Riêng tư'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    /**
     * Accessor cho ảnh đại diện với fallback
     */
    public function getFeaturedImageUrlAttribute()
    {
        if ($this->featured_image) {
            return asset($this->featured_image);
        }
        return asset('assets/frontend/default-images/page-default.jpg');
    }
}
