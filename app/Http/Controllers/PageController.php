<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * <PERSON><PERSON><PERSON> thị trang theo slug
     */
    public function show($slug)
    {
        $page = Page::where('slug', $slug)
                   ->where('status', 'published')
                   ->firstOrFail();

        // Tăng view count nếu cần
        // $page->increment('view_count');

        return view('frontend.page.show', compact('page'));
    }

    /**
     * L<PERSON>y danh sách pages cho menu
     */
    public function getMenuPages()
    {
        return Page::published()
                  ->inMenu()
                  ->get();
    }
}
