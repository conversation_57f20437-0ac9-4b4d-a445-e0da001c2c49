<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\FileUploader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PageController extends Controller
{
    public function __construct()
    {
        date_default_timezone_set('Asia/Ho_Chi_Minh');
    }

    /**
     * Hiển thị danh sách pages
     */
    public function index(Request $request)
    {
        $query = Page::with(['creator', 'updater']);

        // Tìm kiếm
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        // Lọc theo trạng thái
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        $pages = $query->orderBy('sort_order')->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.pages.index', compact('pages'));
    }

    /**
     * Hiển thị form tạo page mới
     */
    public function create()
    {
        return view('admin.pages.create');
    }

    /**
     * Lưu page mới
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required',
            'status' => 'required|in:published,draft,private',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'sort_order' => 'nullable|integer|min:0',
            'template' => 'nullable|string|max:100',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Xử lý slug
        if (empty($data['slug'])) {
            $data['slug'] = Page::generateUniqueSlug($data['title']);
        } else {
            $data['slug'] = Page::generateUniqueSlug($data['slug']);
        }

        // Xử lý upload ảnh đại diện
        if ($request->hasFile('featured_image')) {
            $uploader = new FileUploader();
            $uploader->upload_path = 'uploads/pages/';
            $uploader->file = $request->file('featured_image');
            $uploader->upload();
            $data['featured_image'] = $uploader->result;
        }

        // Xử lý checkbox
        $data['show_in_menu'] = $request->has('show_in_menu');
        $data['sort_order'] = $data['sort_order'] ?? 0;
        $data['template'] = $data['template'] ?? 'default';

        Page::create($data);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Trang đã được tạo thành công!');
    }

    /**
     * Hiển thị form chỉnh sửa page
     */
    public function edit($id)
    {
        $page = Page::findOrFail($id);
        return view('admin.pages.edit', compact('page'));
    }

    /**
     * Cập nhật page
     */
    public function update(Request $request, $id)
    {
        $page = Page::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required',
            'status' => 'required|in:published,draft,private',
            'slug' => 'nullable|string|max:255|unique:pages,slug,' . $id,
            'sort_order' => 'nullable|integer|min:0',
            'template' => 'nullable|string|max:100',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Xử lý slug
        if (empty($data['slug'])) {
            $data['slug'] = Page::generateUniqueSlug($data['title'], $id);
        } else {
            $data['slug'] = Page::generateUniqueSlug($data['slug'], $id);
        }

        // Xử lý upload ảnh đại diện
        if ($request->hasFile('featured_image')) {
            // Xóa ảnh cũ nếu có
            if ($page->featured_image && file_exists(public_path($page->featured_image))) {
                unlink(public_path($page->featured_image));
            }

            $uploader = new FileUploader();
            $uploader->upload_path = 'uploads/pages/';
            $uploader->file = $request->file('featured_image');
            $uploader->upload();
            $data['featured_image'] = $uploader->result;
        }

        // Xử lý checkbox
        $data['show_in_menu'] = $request->has('show_in_menu');
        $data['sort_order'] = $data['sort_order'] ?? 0;
        $data['template'] = $data['template'] ?? 'default';

        $page->update($data);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Trang đã được cập nhật thành công!');
    }

    /**
     * Xóa page
     */
    public function destroy($id)
    {
        $page = Page::findOrFail($id);

        // Xóa ảnh đại diện nếu có
        if ($page->featured_image && file_exists(public_path($page->featured_image))) {
            unlink(public_path($page->featured_image));
        }

        $page->delete();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Trang đã được xóa thành công!');
    }

    /**
     * Thay đổi trạng thái page
     */
    public function toggleStatus($id)
    {
        $page = Page::findOrFail($id);
        
        $newStatus = $page->status === 'published' ? 'draft' : 'published';
        $page->update(['status' => $newStatus]);

        $message = $newStatus === 'published' ? 'Trang đã được xuất bản!' : 'Trang đã chuyển về bản nháp!';

        return redirect()->back()->with('success', $message);
    }

    /**
     * Sao chép page
     */
    public function duplicate($id)
    {
        $originalPage = Page::findOrFail($id);
        
        $newPage = $originalPage->replicate();
        $newPage->title = $originalPage->title . ' (Copy)';
        $newPage->slug = Page::generateUniqueSlug($newPage->title);
        $newPage->status = 'draft';
        $newPage->created_by = auth()->id();
        $newPage->updated_by = null;
        $newPage->save();

        return redirect()->route('admin.pages.edit', $newPage->id)
            ->with('success', 'Trang đã được sao chép thành công!');
    }
}
